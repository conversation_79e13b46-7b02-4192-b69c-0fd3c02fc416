#!/usr/bin/env python3
"""
LoRA Fine-tuning Script per SVG Captioning
Supporta Gemma 2 9B e Llama 3.1 8B con multi-GPU e resume da checkpoint
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path

import torch
import torch.distributed as dist
from torch.utils.data import DataLoader, DistributedSampler
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    TrainingArguments, Trainer,
    BitsAndBytesConfig, DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import Dataset
import wandb

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SVGCaptionDataset:
    """Dataset per SVG captioning"""

    def __init__(self, data_file, tokenizer, max_length=2048):
        self.tokenizer = tokenizer
        self.max_length = max_length

        # Carica dataset
        with open(data_file, 'r') as f:
            self.data = json.load(f)

        logger.info(f"✅ Dataset caricato: {len(self.data)} esempi")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        xml_content = item['xml_content']
        caption = item['caption']

        # Crea prompt basato sul modello
        if "gemma" in self.tokenizer.name_or_path.lower():
            prompt = f"<bos><start_of_turn>user\nGenerate a detailed caption for this SVG image:\n\n{xml_content}<end_of_turn>\n<start_of_turn>model\n{caption}<end_of_turn>"
        elif "llama" in self.tokenizer.name_or_path.lower():
            prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nGenerate a detailed caption for this SVG image:\n\n{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n{caption}<|eot_id|>"
        else:
            prompt = f"Generate a detailed caption for this SVG image:\n\n{xml_content}\n\nCaption: {caption}"

        # Tokenizza
        encoding = self.tokenizer(
            prompt,
            truncation=True,
            max_length=self.max_length,
            padding=False,
            return_tensors="pt"
        )

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': encoding['input_ids'].flatten()
        }

def setup_distributed():
    """Setup distributed training"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])

        dist.init_process_group(backend='nccl', rank=rank, world_size=world_size)
        torch.cuda.set_device(local_rank)

        return rank, world_size, local_rank
    else:
        return 0, 1, 0

def load_config(config_path):
    """Carica configurazione da file JSON"""
    with open(config_path, 'r') as f:
        config = json.load(f)
    return config

def create_lora_config(config):
    """Crea configurazione LoRA"""
    return LoraConfig(
        r=config.get('lora_r', 16),
        lora_alpha=config.get('lora_alpha', 32),
        target_modules=config.get('lora_target_modules', ["q_proj", "k_proj", "v_proj", "o_proj"]),
        lora_dropout=config.get('lora_dropout', 0.1),
        bias="none",
        task_type=TaskType.CAUSAL_LM,
    )

def create_quantization_config(disable_quantization=False):
    """Crea configurazione quantizzazione - SEMPRE ATTIVA per modelli grandi"""
    # FORZA quantizzazione per modelli 8B-9B
    logger.info("🔧 Quantizzazione 4-bit FORZATA per modelli grandi")

    return BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True,
    )

def main():
    parser = argparse.ArgumentParser(description='LoRA Fine-tuning per SVG Captioning')
    parser.add_argument('--model_name_or_path', type=str, required=True, help='Nome o path del modello base')
    parser.add_argument('--data_file', type=str, required=True, help='File dataset JSON')
    parser.add_argument('--config_path', type=str, required=True, help='Path configurazione JSON')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory output')
    parser.add_argument('--disable_quantization', action='store_true', help='Disabilita quantizzazione 4-bit')
    parser.add_argument('--use_wandb', action='store_true', help='Usa WandB per logging')
    parser.add_argument('--wandb_project', type=str, default='svg_captioning', help='Progetto WandB')
    parser.add_argument('--wandb_run_name', type=str, help='Nome run WandB')
    parser.add_argument('--resume_from_checkpoint', type=str, help='Path checkpoint per resume')

    args = parser.parse_args()

    # Setup distributed
    rank, world_size, local_rank = setup_distributed()

    # Carica configurazione
    config = load_config(args.config_path)

    # Setup WandB solo per rank 0
    if args.use_wandb and rank == 0:
        wandb.init(
            project=args.wandb_project,
            name=args.wandb_run_name,
            config=config
        )

    # Carica tokenizer
    logger.info(f"🔧 Caricamento tokenizer: {args.model_name_or_path}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name_or_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Configurazione quantizzazione
    quantization_config = create_quantization_config(args.disable_quantization)

    # Carica modello
    logger.info(f"🔧 Caricamento modello: {args.model_name_or_path}")
    # GEMMA 2 RICHIEDE BFLOAT16 OBBLIGATORIO
    torch_dtype = torch.bfloat16 if "gemma" in args.model_name_or_path.lower() else torch.float16

    model = AutoModelForCausalLM.from_pretrained(
        args.model_name_or_path,
        quantization_config=quantization_config,
        device_map={"": local_rank} if world_size > 1 else "auto",
        torch_dtype=torch_dtype,
        trust_remote_code=True,
        attn_implementation="eager" if "gemma" in args.model_name_or_path.lower() else "flash_attention_2"
    )

    # Prepara modello per training
    if quantization_config:
        model = prepare_model_for_kbit_training(model)

    # Applica LoRA
    lora_config = create_lora_config(config)
    model = get_peft_model(model, lora_config)

    # Dataset
    dataset = SVGCaptionDataset(args.data_file, tokenizer)

    # Training arguments
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        per_device_train_batch_size=config.get('per_device_train_batch_size', 1),
        gradient_accumulation_steps=config.get('gradient_accumulation_steps', 8),
        learning_rate=config.get('learning_rate', 5e-5),
        num_train_epochs=config.get('num_train_epochs', 3),
        max_steps=config.get('max_steps', 45000),
        warmup_steps=config.get('warmup_steps', 500),
        logging_steps=config.get('logging_steps', 50),
        save_steps=config.get('save_steps', 250),
        eval_steps=config.get('eval_steps', 500),
        fp16=False if "gemma" in args.model_name_or_path.lower() else True,
        bf16=True if "gemma" in args.model_name_or_path.lower() else False,
        dataloader_num_workers=4,
        remove_unused_columns=False,
        report_to="wandb" if args.use_wandb else None,
        run_name=args.wandb_run_name,
        ddp_find_unused_parameters=False,
        local_rank=local_rank,
    )

    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )

    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )

    # Resume da checkpoint se specificato
    resume_from_checkpoint = args.resume_from_checkpoint
    if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
        logger.info(f"🔄 Resume da checkpoint: {resume_from_checkpoint}")
    else:
        resume_from_checkpoint = None

    # Avvia training
    logger.info("🚀 Avvio training...")
    trainer.train(resume_from_checkpoint=resume_from_checkpoint)

    # Salva modello finale
    if rank == 0:
        logger.info("💾 Salvataggio modello finale...")
        trainer.save_model()
        tokenizer.save_pretrained(args.output_dir)

    # Cleanup distributed
    if world_size > 1:
        dist.destroy_process_group()

    logger.info("✅ Training completato!")

if __name__ == "__main__":
    main()