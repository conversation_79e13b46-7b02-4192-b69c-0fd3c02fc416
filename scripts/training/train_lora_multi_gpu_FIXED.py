#!/usr/bin/env python3
"""
LoRA Multi-GPU Training Script FIXED - Versione semplificata che funziona
"""

import os
import json
import argparse
import logging
from datetime import datetime

import torch
import torch.distributed as dist
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    TrainingArguments, Trainer,
    BitsAndBytesConfig, DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from datasets import Dataset
import wandb

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SVGDataset:
    def __init__(self, data, tokenizer, max_length=2048):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        xml_content = item['xml']
        caption = item['caption']

        # Prompt basato sul modello
        if "gemma" in self.tokenizer.name_or_path.lower():
            prompt = f"<bos><start_of_turn>user\nDescribe this SVG:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n{caption}<end_of_turn>"
        elif "llama" in self.tokenizer.name_or_path.lower():
            prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescribe this SVG:\n{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n{caption}<|eot_id|>"
        else:
            prompt = f"Describe this SVG:\n{xml_content}\n\nCaption: {caption}"

        # Tokenizza
        encoding = self.tokenizer(
            prompt,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': encoding['input_ids'].flatten()
        }

def setup_distributed():
    """Setup distributed training semplificato"""
    if 'RANK' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
        
        # Inizializza process group
        dist.init_process_group(backend='nccl')
        torch.cuda.set_device(local_rank)
        
        return rank, world_size, local_rank
    else:
        return 0, 1, 0

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_name_or_path', required=True)
    parser.add_argument('--data_file', required=True)
    parser.add_argument('--config_path', required=True)
    parser.add_argument('--output_dir', required=True)
    parser.add_argument('--disable_quantization', action='store_true')
    parser.add_argument('--use_wandb', action='store_true')
    parser.add_argument('--wandb_project', default='svg_captioning')
    parser.add_argument('--wandb_run_name', default='training')
    parser.add_argument('--resume_from_checkpoint', type=str)
    
    args = parser.parse_args()
    
    # Setup distributed
    rank, world_size, local_rank = setup_distributed()
    
    if rank == 0:
        logger.info(f"🚀 Training multi-GPU: rank={rank}, world_size={world_size}, local_rank={local_rank}")
    
    # Carica config
    with open(args.config_path, 'r') as f:
        config = json.load(f)
    
    # Setup WandB solo per rank 0
    if args.use_wandb and rank == 0:
        wandb.init(
            project=args.wandb_project,
            name=args.wandb_run_name,
            config=config
        )
    
    # Carica dataset
    with open(args.data_file, 'r') as f:
        data = json.load(f)
    
    if rank == 0:
        logger.info(f"📊 Dataset caricato: {len(data)} esempi")
    
    # Tokenizer
    tokenizer = AutoTokenizer.from_pretrained(args.model_name_or_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Quantization config
    if not args.disable_quantization:
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True
        )
    else:
        quantization_config = None
    
    # Modello
    torch_dtype = torch.bfloat16 if "gemma" in args.model_name_or_path.lower() else torch.float16
    
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name_or_path,
        quantization_config=quantization_config,
        device_map=None,  # IMPORTANTE: None per multi-GPU!
        torch_dtype=torch_dtype,
        trust_remote_code=True
    )
    
    # LoRA config
    lora_config = LoraConfig(
        r=config['lora_config']['r'],
        lora_alpha=config['lora_config']['lora_alpha'],
        target_modules=config['lora_config']['target_modules'],
        lora_dropout=config['lora_config']['lora_dropout'],
        bias=config['lora_config']['bias'],
        task_type=TaskType.CAUSAL_LM
    )
    
    # Prepara modello per training
    if quantization_config:
        model = prepare_model_for_kbit_training(model)
    
    model = get_peft_model(model, lora_config)
    
    if rank == 0:
        model.print_trainable_parameters()
    
    # Dataset
    svg_dataset = SVGDataset(data, tokenizer)
    dataset_list = []
    for i in range(len(data)):
        dataset_list.append(svg_dataset[i])
    dataset = Dataset.from_list(dataset_list)
    
    # Training arguments
    training_config = config['training_config']
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=training_config['num_train_epochs'],
        per_device_train_batch_size=training_config['per_device_train_batch_size'],
        gradient_accumulation_steps=training_config['gradient_accumulation_steps'],
        learning_rate=training_config['learning_rate'],
        weight_decay=training_config['weight_decay'],
        warmup_ratio=training_config['warmup_ratio'],
        lr_scheduler_type=training_config['lr_scheduler_type'],
        logging_steps=training_config['logging_steps'],
        save_steps=training_config['save_steps'],
        save_total_limit=training_config['save_total_limit'],
        fp16=training_config.get('fp16', True),
        dataloader_num_workers=4,
        remove_unused_columns=False,
        report_to="wandb" if args.use_wandb and rank == 0 else None,
        run_name=args.wandb_run_name,
        ddp_find_unused_parameters=False,
        local_rank=local_rank,
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )
    
    # Resume
    resume_from_checkpoint = args.resume_from_checkpoint
    if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
        if rank == 0:
            logger.info(f"🔄 Resume da checkpoint: {resume_from_checkpoint}")
    else:
        resume_from_checkpoint = None
    
    # Training
    if rank == 0:
        logger.info("🚀 Avvio training...")
    
    trainer.train(resume_from_checkpoint=resume_from_checkpoint)
    
    # Salva modello finale
    if rank == 0:
        logger.info("💾 Salvataggio modello finale...")
        trainer.save_model()
        tokenizer.save_pretrained(args.output_dir)
    
    # Cleanup
    if world_size > 1:
        dist.destroy_process_group()
    
    if rank == 0:
        logger.info("✅ Training completato!")

if __name__ == "__main__":
    main()
