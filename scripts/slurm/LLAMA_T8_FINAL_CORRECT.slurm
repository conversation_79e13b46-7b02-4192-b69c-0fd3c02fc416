#!/bin/bash
#SBATCH --job-name=LLAMA_T8_FINAL
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_FINAL_%j.out
#SBATCH --error=logs/LLAMA_T8_FINAL_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --cpus-per-task=8
#SBATCH --time=24:00:00

echo "🚀 LLAMA T8 TRAINING FINALE - DATASET CORRETTI"
echo "=============================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: 2"
echo "Dataset: FINAL_CORRECT (dai chunks originali)"
echo "=============================================="

# Setup environment
cd /work/tesi_ediluzio
source /opt/miniconda3/etc/profile.d/conda.sh
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false
export TORCH_COMPILE_DISABLE=1
export PYTORCH_DISABLE_DYNAMO=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_FINAL_CORRECT

# Paths
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/llama_t8_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_FINAL_CORRECT"

echo "📊 Configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"
echo "   WandB: $WANDB_RUN_NAME"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Avvio training multi-GPU..."

# Multi-GPU training
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_FINAL_CORRECT

echo "✅ Training completato!"
echo "📁 Output salvato in: $OUTPUT_DIR"
echo "🔗 WandB: https://wandb.ai/337543-unimore/svg_captioning"
