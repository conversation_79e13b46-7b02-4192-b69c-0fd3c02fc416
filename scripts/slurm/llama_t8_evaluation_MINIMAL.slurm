#!/bin/bash
#SBATCH --job-name=LLAMA_T8_MINIMAL
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_MINIMAL_%j.out
#SBATCH --error=logs/LLAMA_T8_MINIMAL_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=64G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=1:00:00

echo "🚀 LLAMA T8 EVALUATION MINIMAL - 10 ESEMPI TEST"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Parametri per evitare errori
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export TOKENIZERS_PARALLELISM=false
export TORCH_COMPILE_DISABLE=1
export PYTORCH_DISABLE_DYNAMO=1
export CUDA_LAUNCH_BLOCKING=1

# Checkpoint Llama T8
CHECKPOINT_PATH="experiments/xml_direct_input/outputs/llama_t8_2gpu_gradient_acc/checkpoint-16750"

echo "✅ Usando checkpoint: $CHECKPOINT_PATH"

# Output directory
OUTPUT_DIR="evaluation_results/llama_t8_minimal"
mkdir -p "$OUTPUT_DIR"

echo "🔄 Inference MINIMAL su 10 esempi..."

# Inference con solo 10 esempi per test
python scripts/evaluation/run_trained_model_inference.py \
    --base_model meta-llama/Llama-3.1-8B-Instruct \
    --checkpoint_path "$CHECKPOINT_PATH" \
    --model_name "Llama_T8_16750_MINIMAL" \
    --test_file data/processed/xml_format_optimized/test_set_corrected_10k.json \
    --max_examples 10 \
    --output_dir "$OUTPUT_DIR"

# Trova file risultati
RESULTS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_16750_MINIMAL_results_*.json" | sort | tail -1)

if [ -z "$RESULTS_FILE" ]; then
    echo "❌ File risultati non trovato"
    exit 1
fi

echo "✅ Risultati inference: $RESULTS_FILE"
echo "🔄 Calcolo metriche..."

# Calcola metriche
python scripts/evaluation/calculate_trained_model_metrics.py \
    --results_file "$RESULTS_FILE" \
    --model_name "Llama_T8_16750_MINIMAL" \
    --output_dir "$OUTPUT_DIR"

# Trova file metriche
METRICS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_16750_MINIMAL_metrics_*.json" | sort | tail -1)

if [ -z "$METRICS_FILE" ]; then
    echo "❌ File metriche non trovato"
    exit 1
fi

echo "✅ Metriche calcolate: $METRICS_FILE"
echo "🔄 Radar chart..."

# Crea radar chart
python scripts/evaluation/create_trained_model_radar.py \
    --trained_metrics "$METRICS_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --output_name "llama_t8_minimal_radar"

echo "🏁 EVALUATION LLAMA T8 MINIMAL COMPLETA"
echo "End time: $(date)"

# Mostra risultati
if [ -f "$METRICS_FILE" ]; then
    echo ""
    echo "📈 METRICHE LLAMA T8 (10 esempi):"
    echo "================================="
    python -c "
import json
with open('$METRICS_FILE', 'r') as f:
    data = json.load(f)
metrics = data.get('metrics', data)
for metric in ['bleu_4', 'meteor', 'cider', 'rouge_l', 'diversity']:
    if metric in metrics:
        value = metrics[metric]
        if isinstance(value, float):
            print(f'  {metric.upper()}: {value:.4f}')
        else:
            print(f'  {metric.upper()}: {value}')
"
fi
