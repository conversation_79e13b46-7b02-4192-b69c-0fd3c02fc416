#!/bin/bash
#SBATCH --job-name=BASELINE_EVAL_400
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/BASELINE_EVAL_400_%j.out
#SBATCH --error=logs/BASELINE_EVAL_400_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=04:00:00

echo "📊 BASELINE EVALUATION - 400 ESEMPI CORRETTI"
echo "============================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Dataset: baseline_set_400_RGB.json (400 esempi)"
echo "Modelli: BLIP-2, Florence2, Idefics3"
echo "============================================="

# Setup environment
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Environment variables
export TOKENIZERS_PARALLELISM=false

# Paths
DATASET_FILE="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB.json"
OUTPUT_DIR="evaluation_results/baseline_400_corretti"

echo "📊 Configurazione:"
echo "   Dataset: $DATASET_FILE"
echo "   Output: $OUTPUT_DIR"
echo "   Esempi: 400 (fisso per baseline)"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Avvio evaluation baseline su 400 esempi..."

# Baseline evaluation
python scripts/evaluation/run_baseline_evaluation_CORRECT.py \
    --dataset_file "$DATASET_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models BLIP2 Florence2 Idefics3 \
    --max_examples 400

echo "✅ Baseline evaluation completata!"
echo "📁 Risultati in: $OUTPUT_DIR"
echo "🖼️ Immagini PNG create da SVG RGB corretti"
echo "📊 HTML report e radar charts generati"
