#!/bin/bash
#SBATCH --job-name=LLAMA_T8_CPU_FIX
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_CPU_FIX_%j.out
#SBATCH --error=logs/LLAMA_T8_CPU_FIX_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=2:00:00

echo "🚀 LLAMA T8 EVALUATION CPU FIX - SOLUZIONE INTERNET"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'✅ GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB')"

# Parametri ottimizzati per CPU merge + GPU inference
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export TOKENIZERS_PARALLELISM=false
export TORCH_COMPILE_DISABLE=1
export PYTORCH_DISABLE_DYNAMO=1
export CUDA_LAUNCH_BLOCKING=1

# Checkpoint Llama T8
CHECKPOINT_PATH="experiments/xml_direct_input/outputs/llama_t8_2gpu_gradient_acc/checkpoint-16750"

echo "✅ Usando checkpoint: $CHECKPOINT_PATH"
echo "🔧 Strategia: CPU merge + GPU inference (soluzione da internet)"

# Output directory
OUTPUT_DIR="evaluation_results/llama_t8_cpu_fix"
mkdir -p "$OUTPUT_DIR"

echo "🔄 Fase 1: Inference con CPU merge fix..."

# Inference con correzione CPU merge (da ricerca internet)
python scripts/evaluation/run_trained_model_inference.py \
    --base_model meta-llama/Llama-3.1-8B-Instruct \
    --checkpoint_path "$CHECKPOINT_PATH" \
    --model_name "Llama_T8_16750_CPU_FIX" \
    --test_file data/processed/xml_format_optimized/test_set_corrected_10k.json \
    --max_examples 50 \
    --output_dir "$OUTPUT_DIR"

# Trova file risultati
RESULTS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_16750_CPU_FIX_results_*.json" | sort | tail -1)

if [ -z "$RESULTS_FILE" ]; then
    echo "❌ File risultati non trovato"
    exit 1
fi

echo "✅ Risultati inference: $RESULTS_FILE"
echo "🔄 Fase 2: Calcolo metriche..."

# Calcola metriche
python scripts/evaluation/calculate_trained_model_metrics.py \
    --results_file "$RESULTS_FILE" \
    --model_name "Llama_T8_16750_CPU_FIX" \
    --output_dir "$OUTPUT_DIR"

# Trova file metriche
METRICS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_16750_CPU_FIX_metrics_*.json" | sort | tail -1)

if [ -z "$METRICS_FILE" ]; then
    echo "❌ File metriche non trovato"
    exit 1
fi

echo "✅ Metriche calcolate: $METRICS_FILE"
echo "🔄 Fase 3: Radar chart..."

# Crea radar chart
python scripts/evaluation/create_trained_model_radar.py \
    --trained_metrics "$METRICS_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --output_name "llama_t8_cpu_fix_vs_baseline_radar"

echo "🏁 EVALUATION LLAMA T8 CPU FIX COMPLETA"
echo "End time: $(date)"
echo "📁 Risultati in: $OUTPUT_DIR"

# Mostra riassunto finale
echo ""
echo "📊 RIASSUNTO EVALUATION LLAMA T8 CPU FIX:"
echo "========================================="
echo "🔹 Checkpoint: $CHECKPOINT_PATH (Step 16750)"
echo "🔹 Strategia: CPU merge + GPU inference"
echo "🔹 Esempi testati: 50"
echo "🔹 Inference: $RESULTS_FILE"
echo "🔹 Metriche: $METRICS_FILE"
echo "🔹 Radar chart: $OUTPUT_DIR/llama_t8_cpu_fix_vs_baseline_radar_*.png"

# Mostra metriche principali
if [ -f "$METRICS_FILE" ]; then
    echo ""
    echo "📈 METRICHE PRINCIPALI LLAMA T8:"
    echo "==============================="
    python -c "
import json
with open('$METRICS_FILE', 'r') as f:
    data = json.load(f)
metrics = data.get('metrics', data)
for metric in ['bleu_4', 'meteor', 'cider', 'rouge_l', 'diversity']:
    if metric in metrics:
        value = metrics[metric]
        if isinstance(value, float):
            print(f'  {metric.upper()}: {value:.4f}')
        else:
            print(f'  {metric.upper()}: {value}')
"
fi
