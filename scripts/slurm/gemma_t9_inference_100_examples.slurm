#!/bin/bash
#SBATCH --job-name=LLAMA_T8_MERGED
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_MERGED_%j.out
#SBATCH --error=logs/LLAMA_T8_MERGED_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=2:00:00

echo "🚀 LLAMA T8 MERGED EVALUATION - 100 ESEMPI"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'✅ GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB')"

# Parametri
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export TOKENIZERS_PARALLELISM=false

# Modello Llama T8 merged
MERGED_MODEL="models/llama_t8_merged_16750"

echo "✅ Usando modello merged: $MERGED_MODEL"

# Output directory
OUTPUT_DIR="evaluation_results/llama_t8_merged_100"
mkdir -p "$OUTPUT_DIR"

echo "🔄 Fase 1: Inference su 100 esempi..."

# Inference con modello merged (100 esempi)
python scripts/evaluation/run_merged_model_inference.py \
    --model_path "$MERGED_MODEL" \
    --model_name "Llama_T8_Merged_16750" \
    --test_file data/processed/xml_format_optimized/test_set_corrected_10k.json \
    --max_examples 100 \
    --output_dir "$OUTPUT_DIR"

# Trova file risultati più recente
RESULTS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_Merged_16750_results_*.json" | sort | tail -1)

if [ -z "$RESULTS_FILE" ]; then
    echo "❌ File risultati non trovato"
    exit 1
fi

echo "✅ Risultati inference: $RESULTS_FILE"
echo "🔄 Fase 2: Calcolo metriche..."

# Calcola metriche
python scripts/evaluation/calculate_trained_model_metrics.py \
    --results_file "$RESULTS_FILE" \
    --model_name "Llama_T8_Merged_16750" \
    --output_dir "$OUTPUT_DIR"

# Trova file metriche più recente
METRICS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_Merged_16750_metrics_*.json" | sort | tail -1)

if [ -z "$METRICS_FILE" ]; then
    echo "❌ File metriche non trovato"
    exit 1
fi

echo "✅ Metriche calcolate: $METRICS_FILE"
echo "🔄 Fase 3: Creazione radar chart..."

# Crea radar chart confrontando con baseline
python scripts/evaluation/create_trained_model_radar.py \
    --trained_metrics "$METRICS_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --output_name "llama_t8_merged_vs_baseline_radar"

echo "🏁 EVALUATION LLAMA T8 MERGED COMPLETA"
echo "End time: $(date)"
echo "📁 Risultati in: $OUTPUT_DIR"

# Mostra riassunto finale
echo ""
echo "📊 RIASSUNTO EVALUATION LLAMA T8 MERGED:"
echo "======================================="
echo "🔹 Modello: $MERGED_MODEL (Step 16750)"
echo "🔹 Esempi testati: 100"
echo "🔹 Inference: $RESULTS_FILE"
echo "🔹 Metriche: $METRICS_FILE"
echo "🔹 Radar chart: $OUTPUT_DIR/llama_t8_merged_vs_baseline_radar_*.png"

# Mostra metriche principali
if [ -f "$METRICS_FILE" ]; then
    echo ""
    echo "📈 METRICHE PRINCIPALI:"
    echo "======================"
    python -c "
import json
with open('$METRICS_FILE', 'r') as f:
    data = json.load(f)
metrics = data.get('metrics', data)
for metric in ['bleu_4', 'meteor', 'cider', 'rouge_l', 'diversity']:
    if metric in metrics:
        value = metrics[metric]
        if isinstance(value, float):
            print(f'  {metric.upper()}: {value:.4f}')
        else:
            print(f'  {metric.upper()}: {value}')
"
fi
