#!/bin/bash
#SBATCH --job-name=GEMMA_T9_RESUME
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_SINGLE_QUANT_%j.out
#SBATCH --error=logs/GEMMA_T9_SINGLE_QUANT_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 GEMMA T9 SINGLE GPU QUANTIZED - SOLUZIONE DEFINITIVA"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'✅ GPU count: {torch.cuda.device_count()}')"

# Parametri ottimizzati per Gemma 2 9B single GPU
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_resume_from_1750
export TOKENIZERS_PARALLELISM=false
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_single_gpu_quantized"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio GEMMA T9 SINGLE GPU CON QUANTIZZAZIONE..."

# Training single GPU con quantizzazione (SOLUZIONE CHE FUNZIONA)
python scripts/training/train_lora_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_single_gpu_quantized.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_resume_from_1750 \
    --resume_from_checkpoint "$OUTPUT_DIR/checkpoint-1750"

echo "🏁 GEMMA T9 SINGLE GPU TRAINING COMPLETATO"
echo "End time: $(date)"
echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
