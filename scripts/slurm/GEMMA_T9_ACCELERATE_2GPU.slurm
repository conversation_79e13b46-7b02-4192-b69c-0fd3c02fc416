#!/bin/bash
#SBATCH --job-name=GEMMA_T9_ACCEL
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_ACCEL_%j.out
#SBATCH --error=logs/GEMMA_T9_ACCEL_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --time=24:00:00

echo "🚀 GEMMA T9 TRAINING CON ACCELERATE - 2 GPU"
echo "============================================"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: 2 (con Accelerate)"
echo "Dataset: FINAL_CORRECT_RGB (dai chunks originali)"
echo "============================================"

# Setup environment
cd /work/tesi_ediluzio
source /opt/miniconda3/etc/profile.d/conda.sh
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false
export TORCH_COMPILE_DISABLE=1
export PYTORCH_DISABLE_DYNAMO=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_accelerate_2gpu

# Paths
MODEL_NAME="google/gemma-2-9b-it"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_ACCELERATE_2GPU"

echo "📊 Configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"
echo "   WandB: $WANDB_RUN_NAME"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Avvio training con Accelerate (2 GPU)..."

# Training con Accelerate
accelerate launch \
    --num_processes=2 \
    --num_machines=1 \
    --machine_rank=0 \
    --main_process_port=29501 \
    scripts/training/train_lora_accelerate_2gpu.py \
    --model_name_or_path "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_accelerate_2gpu

echo "✅ Training completato!"
echo "📁 Output salvato in: $OUTPUT_DIR"
echo "🔗 WandB: https://wandb.ai/337543-unimore/svg_captioning"
