#!/bin/bash
#SBATCH --job-name=LLAMA_T8_INFERENCE
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_INFERENCE_%j.out
#SBATCH --error=logs/LLAMA_T8_INFERENCE_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=4:00:00

echo "🚀 LLAMA T8 INFERENCE - EVALUATION COMPLETA"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Checkpoint Llama T8
CHECKPOINT_PATH="experiments/xml_direct_input/outputs/llama_t8_2gpu_gradient_acc/checkpoint-16750"

echo "✅ Usando checkpoint: $CHECKPOINT_PATH"

# Output directory
OUTPUT_DIR="evaluation_results/llama_evaluation"
mkdir -p "$OUTPUT_DIR"

echo "🔄 Fase 1: Inference..."

# Inference con 50 esempi per evitare problemi memoria
python scripts/evaluation/run_trained_model_inference.py \
    --base_model meta-llama/Llama-3.1-8B-Instruct \
    --checkpoint_path "$CHECKPOINT_PATH" \
    --model_name "Llama_T8_16750" \
    --test_file data/processed/xml_format_optimized/test_set_corrected_10k.json \
    --max_examples 50 \
    --output_dir "$OUTPUT_DIR"

# Trova file risultati più recente
RESULTS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_16750_results_*.json" | sort | tail -1)

if [ -z "$RESULTS_FILE" ]; then
    echo "❌ File risultati non trovato"
    exit 1
fi

echo "✅ Risultati inference: $RESULTS_FILE"
echo "🔄 Fase 2: Calcolo metriche..."

# Calcola metriche
python scripts/evaluation/calculate_trained_model_metrics.py \
    --results_file "$RESULTS_FILE" \
    --model_name "Llama_T8_16750" \
    --output_dir "$OUTPUT_DIR"

# Trova file metriche più recente
METRICS_FILE=$(find "$OUTPUT_DIR" -name "Llama_T8_16750_metrics_*.json" | sort | tail -1)

if [ -z "$METRICS_FILE" ]; then
    echo "❌ File metriche non trovato"
    exit 1
fi

echo "✅ Metriche calcolate: $METRICS_FILE"
echo "🔄 Fase 3: Creazione radar chart..."

# Crea radar chart
python scripts/evaluation/create_trained_model_radar.py \
    --trained_metrics "$METRICS_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --output_name "llama_t8_vs_baseline_radar"

echo "🏁 EVALUATION LLAMA T8 COMPLETA"
echo "End time: $(date)"
echo "📁 Risultati in: $OUTPUT_DIR"

# Mostra riassunto finale
echo ""
echo "📊 RIASSUNTO EVALUATION LLAMA T8:"
echo "================================="
echo "🔹 Checkpoint: $CHECKPOINT_PATH"
echo "🔹 Inference: $RESULTS_FILE"
echo "🔹 Metriche: $METRICS_FILE"
echo "🔹 Radar chart: $OUTPUT_DIR/llama_t8_vs_baseline_radar_*.png"
