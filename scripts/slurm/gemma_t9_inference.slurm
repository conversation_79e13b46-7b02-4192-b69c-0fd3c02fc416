#!/bin/bash
#SBATCH --job-name=GEMMA_T9_INFERENCE
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_INFERENCE_%j.out
#SBATCH --error=logs/GEMMA_T9_INFERENCE_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=4:00:00

echo "🚀 GEMMA T9 INFERENCE - EVALUATION COMPLETA"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Trova ultimo checkpoint
CHECKPOINT_DIR="experiments/xml_direct_input/outputs/gemma_t9_single_gpu_quantized"
LATEST_CHECKPOINT=$(find "$CHECKPOINT_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)

if [ -z "$LATEST_CHECKPOINT" ]; then
    echo "❌ Nessun checkpoint trovato in $CHECKPOINT_DIR"
    exit 1
fi

echo "✅ Usando checkpoint: $LATEST_CHECKPOINT"

# Output directory
OUTPUT_DIR="evaluation_results/trained_models"
mkdir -p "$OUTPUT_DIR"

echo "🔄 Fase 1: Inference..."

# Inference
python scripts/evaluation/run_trained_model_inference.py \
    --base_model google/gemma-2-9b-it \
    --checkpoint_path "$LATEST_CHECKPOINT" \
    --model_name "Gemma_T9_Trained" \
    --test_file data/processed/xml_format_optimized/test_set_corrected_10k.json \
    --max_examples 400 \
    --output_dir "$OUTPUT_DIR"

# Trova file risultati più recente
RESULTS_FILE=$(find "$OUTPUT_DIR" -name "Gemma_T9_Trained_results_*.json" | sort | tail -1)

if [ -z "$RESULTS_FILE" ]; then
    echo "❌ File risultati non trovato"
    exit 1
fi

echo "✅ Risultati inference: $RESULTS_FILE"
echo "🔄 Fase 2: Calcolo metriche..."

# Calcola metriche
python scripts/evaluation/calculate_trained_model_metrics.py \
    --results_file "$RESULTS_FILE" \
    --model_name "Gemma_T9_Trained" \
    --output_dir "$OUTPUT_DIR"

# Trova file metriche più recente
METRICS_FILE=$(find "$OUTPUT_DIR" -name "Gemma_T9_Trained_metrics_*.json" | sort | tail -1)

if [ -z "$METRICS_FILE" ]; then
    echo "❌ File metriche non trovato"
    exit 1
fi

echo "✅ Metriche calcolate: $METRICS_FILE"
echo "🔄 Fase 3: Creazione radar chart..."

# Crea radar chart
python scripts/evaluation/create_trained_model_radar.py \
    --trained_metrics "$METRICS_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --output_name "gemma_t9_vs_baseline_radar"

echo "🏁 EVALUATION COMPLETA"
echo "End time: $(date)"
echo "📁 Risultati in: $OUTPUT_DIR"

# Mostra riassunto finale
echo ""
echo "📊 RIASSUNTO EVALUATION:"
echo "========================"
echo "🔹 Inference: $RESULTS_FILE"
echo "🔹 Metriche: $METRICS_FILE"
echo "🔹 Radar chart: $OUTPUT_DIR/gemma_t9_vs_baseline_radar_*.png"
