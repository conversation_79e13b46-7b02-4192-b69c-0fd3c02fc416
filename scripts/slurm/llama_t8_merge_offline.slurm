#!/bin/bash
#SBATCH --job-name=LLAMA_MERGE
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_MERGE_%j.out
#SBATCH --error=logs/LLAMA_MERGE_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=2:00:00

echo "🚀 LLAMA T8 MERGE OFFLINE - SOLUZIONE HUGGINGFACE FORUM"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

# Parametri
BASE_MODEL="meta-llama/Llama-3.1-8B-Instruct"
LORA_ADAPTER="experiments/xml_direct_input/outputs/llama_t8_2gpu_gradient_acc/checkpoint-16750"
OUTPUT_PATH="models/llama_t8_merged_16750"

echo "✅ Base model: $BASE_MODEL"
echo "✅ LoRA adapter: $LORA_ADAPTER"
echo "✅ Output: $OUTPUT_PATH"

# Merge offline
python scripts/evaluation/merge_llama_offline.py \
    --base_model "$BASE_MODEL" \
    --lora_adapter "$LORA_ADAPTER" \
    --output_path "$OUTPUT_PATH"

echo "🏁 MERGE OFFLINE COMPLETATO"
echo "End time: $(date)"

# Verifica risultato
if [ -d "$OUTPUT_PATH" ]; then
    echo "✅ Modello merged salvato in: $OUTPUT_PATH"
    ls -la "$OUTPUT_PATH"
else
    echo "❌ Errore: modello merged non trovato"
    exit 1
fi
