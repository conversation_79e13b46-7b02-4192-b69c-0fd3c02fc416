#!/bin/bash
#SBATCH --job-name=GEMMA_T9_2GPU_QUANT
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_2GPU_QUANT_%j.out
#SBATCH --error=logs/GEMMA_T9_2GPU_QUANT_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --time=24:00:00

echo "🚀 GEMMA T9 - 2 GPU QUANTIZED - DATASET CORRETTI"
echo "================================================"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: 2 (quantized)"
echo "Dataset: FINAL_CORRECT_RGB"
echo "================================================"

# Setup environment
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false
export TORCH_COMPILE_DISABLE=1
export PYTORCH_DISABLE_DYNAMO=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_2gpu_quantized

# Paths
MODEL_NAME="google/gemma-2-9b-it"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_2gpu_quantized"

echo "📊 Configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"

# Create directories
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Avvio training 2 GPU quantized..."

# Training con quantizzazione (PIÙ STABILE)
python scripts/training/train_lora_simple.py \
    --model_name_or_path "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_2gpu_quantized

echo "✅ Training completato!"
echo "📁 Output: $OUTPUT_DIR"
