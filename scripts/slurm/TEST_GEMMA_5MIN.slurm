#!/bin/bash
#SBATCH --job-name=TEST_GEMMA_5MIN
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_GEMMA_5MIN_%j.out
#SBATCH --error=logs/TEST_GEMMA_5MIN_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --time=00:05:00

echo "🧪 TEST GEMMA T9 - 5 MINUTI"
echo "=========================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: 2"

# Setup environment
cd /work/tesi_ediluzio
source /opt/miniconda3/etc/profile.d/conda.sh
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false
export TORCH_COMPILE_DISABLE=1
export PYTORCH_DISABLE_DYNAMO=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=test_gemma_5min

# Paths
MODEL_NAME="google/gemma-2-9b-it"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/TEST_GEMMA_5MIN"

echo "📊 Test configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Test multi-GPU training..."

# Multi-GPU training
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29501 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name test_gemma_5min

echo "✅ Test completato!"
