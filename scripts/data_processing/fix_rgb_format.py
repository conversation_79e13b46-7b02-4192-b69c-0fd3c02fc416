#!/usr/bin/env python3
"""
Corregge il formato RGB nei dataset usando le funzioni del codice fornito
"""

import json
import re
import os

def replacer(match):
    """Converte fill:255,0,0 -> fill:rgb(255,0,0)"""
    if "stroke" in match.group(0):
        new_s = match.group(0).replace("stroke:", "stroke:rgb(") + ")"
    elif "fill" in match.group(0):
        new_s = match.group(0).replace("fill:", "fill:rgb(") + ")"
    return new_s

def fix_svg_colors(svg_data):
    """Applica la correzione RGB al campo XML"""
    # Applica le regex per convertire i colori
    svg_data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, svg_data)
    return svg_data

def de_parser(data):
    """Converte SVG data in XML visualizzabile (dal codice fornito)"""
    # Header
    res = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<svg viewBox=\"0  0 512  512\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\">\n"
    
    data = data.replace("style=", "<path style=\"")
    data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, data)
    data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, data)
    data = data.replace("\t", "\" d=\"")
    data = data.replace("\n", "Z\" />\n")
    
    res += data
    
    # Footer
    res += "</svg>"
    
    return res

def fix_dataset(input_file, output_file):
    """Corregge un dataset applicando il formato RGB corretto"""
    print(f"🔄 Correzione {input_file} -> {output_file}")
    
    # Carica dataset
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    print(f"📊 Dataset: {len(data)} esempi")
    
    # Correggi ogni esempio
    fixed_data = []
    for i, example in enumerate(data):
        if i % 10000 == 0:
            print(f"   📈 Processati {i}/{len(data)} esempi...")
        
        # Correggi il campo XML
        fixed_xml = fix_svg_colors(example['xml'])
        
        # Crea esempio corretto
        fixed_example = {
            'xml': fixed_xml,
            'caption': example['caption'],
            'filename': example.get('filename', '')
        }
        
        fixed_data.append(fixed_example)
    
    # Salva dataset corretto
    with open(output_file, 'w') as f:
        json.dump(fixed_data, f, indent=2)
    
    print(f"✅ Dataset corretto salvato: {output_file}")
    
    # Verifica
    print(f"🔍 Verifica primo esempio:")
    print(f"   Prima: {data[0]['xml'][:100]}...")
    print(f"   Dopo:  {fixed_data[0]['xml'][:100]}...")
    print(f"   Ha rgb(): {'rgb(' in fixed_data[0]['xml']}")

def main():
    input_dir = "data/processed/FINAL_CORRECT"
    output_dir = "data/processed/FINAL_CORRECT_RGB"
    
    # Crea directory output
    os.makedirs(output_dir, exist_ok=True)
    
    print("🚀 CORREZIONE FORMATO RGB NEI DATASET")
    print("=" * 50)
    
    # Lista dei file da correggere
    files_to_fix = [
        ("train_set_90k.json", "train_set_90k_RGB.json"),
        ("test_set_10k.json", "test_set_10k_RGB.json"),
        ("baseline_set_400.json", "baseline_set_400_RGB.json")
    ]
    
    for input_name, output_name in files_to_fix:
        input_path = os.path.join(input_dir, input_name)
        output_path = os.path.join(output_dir, output_name)
        
        if os.path.exists(input_path):
            fix_dataset(input_path, output_path)
        else:
            print(f"⚠️ File non trovato: {input_path}")
    
    print("=" * 50)
    print("🎉 CORREZIONE RGB COMPLETATA!")
    print(f"📁 Dataset corretti in: {output_dir}")
    print("🔥 Ora i dataset hanno il formato RGB corretto!")

if __name__ == "__main__":
    main()
