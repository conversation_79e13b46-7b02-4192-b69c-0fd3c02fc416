#!/usr/bin/env python3
"""
Evaluation baseline con dataset corretti RGB
"""

import json
import os
import argparse
from datetime import datetime
import subprocess
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_images_from_svg(dataset_file, output_dir):
    """Crea immagini PNG dai dati SVG corretti"""
    logger.info(f"🖼️ Creazione immagini da {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Funzione de_parser dal codice fornito CON SFONDO BIANCO
    def de_parser(svg_data):
        res = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<svg viewBox=\"0  0 512  512\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\">\n"

        # IMPORTANTE: AGGIUNGO SFONDO BIANCO!
        res += "<rect width=\"512\" height=\"512\" fill=\"white\"/>\n"

        svg_data = svg_data.replace("style=", "<path style=\"")
        svg_data = svg_data.replace("\t", "\" d=\"")
        svg_data = svg_data.replace("\n", "Z\" />\n")

        res += svg_data
        res += "</svg>"

        return res
    
    created_images = []
    
    for i, example in enumerate(data):
        if i % 50 == 0:
            logger.info(f"   📈 Processate {i}/{len(data)} immagini...")
        
        # Converte SVG in XML completo
        svg_xml = de_parser(example['xml'])
        
        # Salva SVG temporaneo
        svg_file = os.path.join(output_dir, f"temp_{i}.svg")
        with open(svg_file, 'w') as f:
            f.write(svg_xml)
        
        # Converte in PNG usando cairosvg o inkscape
        png_file = os.path.join(output_dir, f"image_{i:04d}.png")
        
        try:
            # Prova con cairosvg CON SFONDO BIANCO
            import cairosvg
            cairosvg.svg2png(
                url=svg_file,
                write_to=png_file,
                output_width=512,
                output_height=512,
                background_color='white'  # SFONDO BIANCO!
            )
        except ImportError:
            try:
                # Fallback con inkscape CON SFONDO BIANCO
                subprocess.run([
                    'inkscape', svg_file,
                    '--export-type=png',
                    f'--export-filename={png_file}',
                    '--export-width=512',
                    '--export-height=512',
                    '--export-background=white',  # SFONDO BIANCO!
                    '--export-background-opacity=1.0'
                ], check=True, capture_output=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning(f"⚠️ Impossibile convertire immagine {i}")
                continue
        
        # Rimuovi SVG temporaneo
        os.remove(svg_file)
        
        # Aggiungi info immagine
        created_images.append({
            'id': i,
            'image_path': png_file,
            'caption': example['caption'],
            'filename': example.get('filename', f'image_{i:04d}')
        })
    
    logger.info(f"✅ Create {len(created_images)} immagini in {output_dir}")
    return created_images

def run_baseline_model(model_name, images_data, output_dir):
    """Esegue evaluation con un modello baseline"""
    logger.info(f"🔄 Evaluation {model_name}...")
    
    # Qui andrà l'implementazione specifica per ogni modello baseline
    # BLIP-2, Florence2, Idefics3
    
    results = []
    for i, img_data in enumerate(images_data[:10]):  # Test con 10 immagini
        # Placeholder per ora
        result = {
            'id': img_data['id'],
            'image_path': img_data['image_path'],
            'ground_truth': img_data['caption'],
            'prediction': f"[{model_name}] Placeholder prediction for image {i}",
            'model': model_name
        }
        results.append(result)
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(output_dir, f"{model_name}_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ {model_name} completato: {results_file}")
    return results_file

def main():
    parser = argparse.ArgumentParser(description="Baseline evaluation con dataset corretti")
    parser.add_argument("--dataset_file", required=True, help="File dataset corretti RGB")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    parser.add_argument("--models", nargs='+', default=['BLIP2', 'Florence2', 'Idefics3'], help="Modelli baseline")
    parser.add_argument("--max_examples", type=int, default=400, help="Numero esempi baseline (fisso a 400)")
    
    args = parser.parse_args()
    
    logger.info("🚀 BASELINE EVALUATION CON DATASET CORRETTI")
    logger.info("=" * 50)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    images_dir = os.path.join(args.output_dir, "images")
    
    # Crea immagini dai dati SVG corretti
    images_data = create_images_from_svg(args.dataset_file, images_dir)
    
    # Verifica che abbiamo esattamente 400 esempi per baseline
    if len(images_data) != 400:
        logger.warning(f"⚠️ Dataset baseline ha {len(images_data)} esempi, dovrebbe avere 400!")

    # Usa tutti i 400 esempi per baseline
    logger.info(f"📊 Dataset baseline: {len(images_data)} esempi")
    
    # Esegue evaluation per ogni modello baseline
    results_files = []
    for model_name in args.models:
        results_file = run_baseline_model(model_name, images_data, args.output_dir)
        results_files.append(results_file)
    
    logger.info("=" * 50)
    logger.info("🎉 BASELINE EVALUATION COMPLETATA!")
    logger.info(f"📁 Risultati in: {args.output_dir}")
    logger.info(f"🖼️ Immagini in: {images_dir}")
    for rf in results_files:
        logger.info(f"📊 Risultati: {rf}")

if __name__ == "__main__":
    main()
