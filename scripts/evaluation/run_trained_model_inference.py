#!/usr/bin/env python3
"""
Script per fare inference con modelli trained (Gemma T9, Llama T8, etc.)
Usa il dataset di test e genera predizioni per calcolare metriche
"""

import json
import torch
import argparse
from datetime import datetime
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import logging

# Disabilita completamente torch.compile e dynamo per PEFT
import torch._dynamo
torch._dynamo.config.suppress_errors = True
torch._dynamo.config.disable = True

# Disabilita torch.compile globalmente
import torch
torch.set_float32_matmul_precision('high')
torch.backends.cuda.matmul.allow_tf32 = True

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_trained_model(base_model_path, checkpoint_path, device="cuda"):
    """Carica modello trained con LoRA"""
    logger.info(f"🔧 Caricamento tokenizer: {base_model_path}")
    tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    logger.info(f"🔧 Caricamento modello base: {base_model_path}")
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        torch_dtype=torch.float16,
        device_map="cpu",  # Carica su CPU per evitare problemi con LoRA merge
        trust_remote_code=True,
        attn_implementation="eager"  # Forza eager attention per evitare problemi
    )

    logger.info(f"🔧 Caricamento LoRA checkpoint: {checkpoint_path}")

    # Fix per bug PEFT - crea config pulita
    import json
    from pathlib import Path

    adapter_config_path = Path(checkpoint_path) / "adapter_config.json"
    if adapter_config_path.exists():
        with open(adapter_config_path, 'r') as f:
            config = json.load(f)

        # Mantieni solo campi essenziali per LoRA
        essential_fields = {
            'base_model_name_or_path': config.get('base_model_name_or_path'),
            'bias': config.get('bias', 'none'),
            'fan_in_fan_out': config.get('fan_in_fan_out', False),
            'inference_mode': config.get('inference_mode', True),
            'init_lora_weights': config.get('init_lora_weights', True),
            'lora_alpha': config.get('lora_alpha', 32),
            'lora_dropout': config.get('lora_dropout', 0.1),
            'peft_type': config.get('peft_type', 'LORA'),
            'r': config.get('r', 16),
            'target_modules': config.get('target_modules', []),
            'task_type': config.get('task_type', 'CAUSAL_LM'),
            'use_dora': config.get('use_dora', False),
            'use_rslora': config.get('use_rslora', False)
        }

        logger.info(f"🔧 Creazione config LoRA pulita")

        # Salva config corretta temporaneamente
        temp_config_path = adapter_config_path.with_suffix('.json.backup')
        with open(temp_config_path, 'w') as f:
            json.dump(essential_fields, f, indent=2)

        # Sostituisci config originale
        adapter_config_path.rename(adapter_config_path.with_suffix('.json.original'))
        temp_config_path.rename(adapter_config_path)

    try:
        model = PeftModel.from_pretrained(base_model, checkpoint_path)
        logger.info(f"🔧 Merge LoRA weights...")
        model = model.merge_and_unload()  # Merge LoRA weights

        # Sposta il modello merged su GPU
        logger.info(f"🔧 Spostamento modello su GPU...")
        model = model.to("cuda")

    finally:
        # Ripristina config originale
        if adapter_config_path.with_suffix('.json.original').exists():
            adapter_config_path.rename(adapter_config_path.with_suffix('.json.temp'))
            adapter_config_path.with_suffix('.json.original').rename(adapter_config_path)
            adapter_config_path.with_suffix('.json.temp').unlink()

    return model, tokenizer

def create_prompt(xml_content, model_name):
    """Crea prompt appropriato per il modello"""
    if "gemma" in model_name.lower():
        return f"<start_of_turn>user\nDescribe this SVG:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n"
    elif "llama" in model_name.lower():
        return f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescribe this SVG:\n{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
    else:
        return f"Describe this SVG:\n{xml_content}\n\nDescription: "

def generate_caption(model, tokenizer, xml_content, model_name, max_length=512):
    """Genera caption per un SVG"""
    prompt = create_prompt(xml_content, model_name)
    
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    # Estrai solo la parte generata (rimuovi prompt)
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Rimuovi il prompt dalla risposta
    if "gemma" in model_name.lower():
        if "<start_of_turn>model\n" in generated_text:
            caption = generated_text.split("<start_of_turn>model\n")[-1].strip()
        else:
            caption = generated_text[len(prompt):].strip()
    elif "llama" in model_name.lower():
        if "<|start_header_id|>assistant<|end_header_id|>" in generated_text:
            caption = generated_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        else:
            caption = generated_text[len(prompt):].strip()
    else:
        caption = generated_text[len(prompt):].strip()
    
    # Pulisci la caption
    if caption.endswith("<|eot_id|>"):
        caption = caption[:-10].strip()
    if caption.endswith("<end_of_turn>"):
        caption = caption[:-13].strip()
    
    return caption

def main():
    parser = argparse.ArgumentParser(description="Inference con modelli trained")
    parser.add_argument("--base_model", type=str, required=True, help="Modello base (es: google/gemma-2-9b-it)")
    parser.add_argument("--checkpoint_path", type=str, required=True, help="Path al checkpoint LoRA")
    parser.add_argument("--model_name", type=str, required=True, help="Nome del modello per output")
    parser.add_argument("--test_file", type=str, default="data/processed/xml_format_optimized/test_set_corrected_10k.json")
    parser.add_argument("--max_examples", type=int, default=400, help="Numero massimo di esempi da processare")
    parser.add_argument("--output_dir", type=str, default="evaluation_results/trained_models")
    args = parser.parse_args()
    
    # Setup
    device = "cuda" if torch.cuda.is_available() else "cpu"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Carica modello
    logger.info(f"🚀 Avvio inference per {args.model_name}")
    model, tokenizer = load_trained_model(args.base_model, args.checkpoint_path, device)
    
    # Carica dataset di test
    logger.info(f"📊 Caricamento dataset: {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)
    
    # Limita esempi se richiesto
    if args.max_examples and len(test_data) > args.max_examples:
        test_data = test_data[:args.max_examples]
        logger.info(f"📊 Limitato a {args.max_examples} esempi")
    
    # Inference
    results = []
    logger.info(f"🔄 Inizio inference su {len(test_data)} esempi...")
    
    for i, item in enumerate(test_data):
        if i % 50 == 0:
            logger.info(f"📈 Progresso: {i}/{len(test_data)} ({i/len(test_data)*100:.1f}%)")
        
        try:
            xml_content = item['xml']
            ground_truth = item['caption']
            
            # Genera predizione
            prediction = generate_caption(model, tokenizer, xml_content, args.model_name)
            
            results.append({
                "id": item.get('id', f'item_{i}'),
                "xml": xml_content,
                "ground_truth": ground_truth,
                "prediction": prediction
            })
            
        except Exception as e:
            logger.error(f"❌ Errore al sample {i}: {e}")
            results.append({
                "id": item.get('id', f'item_{i}'),
                "xml": item['xml'],
                "ground_truth": item['caption'],
                "prediction": f"ERROR: {str(e)}"
            })
    
    # Salva risultati
    output_file = output_dir / f"{args.model_name}_results_{timestamp}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Inference completata!")
    logger.info(f"📁 Risultati salvati in: {output_file}")
    logger.info(f"📊 Esempi processati: {len(results)}")
    
    # Statistiche rapide
    successful = sum(1 for r in results if not r['prediction'].startswith('ERROR:'))
    logger.info(f"📈 Success rate: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")

if __name__ == "__main__":
    main()
