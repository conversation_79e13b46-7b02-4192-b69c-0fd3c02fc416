#!/usr/bin/env python3
"""
Inference con modello merged (senza LoRA) - SOLUZIONE DEFINITIVA
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import json
import argparse
from datetime import datetime
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_merged_model(model_path):
    """Carica modello merged (standard, senza LoRA)"""
    logger.info(f"🔧 Caricamento modello merged: {model_path}")
    
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    logger.info(f"✅ Modello merged caricato con successo")
    return model, tokenizer

def run_inference(model, tokenizer, test_data, max_examples, model_name):
    """Esegue inference su test data"""
    logger.info(f"🔄 Inizio inference su {min(max_examples, len(test_data))} esempi...")
    
    results = []
    
    for i, example in enumerate(test_data[:max_examples]):
        if i % 10 == 0:
            logger.info(f"📈 Progresso: {i}/{max_examples} ({i/max_examples*100:.1f}%)")
        
        # Input prompt
        svg_input = example.get('svg', example.get('input', ''))
        prompt = f"Generate a caption for this SVG: {svg_input}"
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=150,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Decode
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        prediction = generated_text[len(prompt):].strip()
        
        # Salva risultato
        result = {
            'id': i,
            'svg': svg_input,
            'ground_truth': example.get('caption', example.get('output', '')),
            'prediction': prediction,
            'model': model_name
        }
        results.append(result)
    
    logger.info(f"✅ Inference completata su {len(results)} esempi")
    return results

def main():
    parser = argparse.ArgumentParser(description="Inference con modello merged")
    parser.add_argument("--model_path", required=True, help="Path al modello merged")
    parser.add_argument("--model_name", required=True, help="Nome del modello")
    parser.add_argument("--test_file", required=True, help="File test JSON")
    parser.add_argument("--max_examples", type=int, default=100, help="Numero massimo esempi")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    
    args = parser.parse_args()
    
    # Carica modello merged
    model, tokenizer = load_merged_model(args.model_path)
    
    # Carica test data
    logger.info(f"📊 Caricamento dataset: {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)
    logger.info(f"📊 Limitato a {args.max_examples} esempi")
    
    # Inference
    results = run_inference(model, tokenizer, test_data, args.max_examples, args.model_name)
    
    # Salva risultati
    os.makedirs(args.output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f"{args.model_name}_results_{timestamp}.json")
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Risultati salvati in: {output_file}")

if __name__ == "__main__":
    main()
