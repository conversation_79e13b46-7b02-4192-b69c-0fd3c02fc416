_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.10.18
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 63
                - 71
                - 98
            "3":
                - 13
                - 16
                - 55
            "4": 3.10.18
            "5": 0.20.1
            "6": 4.53.1
            "12": 0.20.1
            "13": linux-x86_64
data_config:
    value:
        max_length: 2048
        padding: max_length
        truncation: true
lora_config:
    value:
        bias: none
        lora_alpha: 128
        lora_dropout: 0.05
        r: 64
        target_modules:
            - q_proj
            - k_proj
            - v_proj
            - o_proj
            - gate_proj
            - up_proj
            - down_proj
        task_type: CAUSAL_LM
model_name:
    value: google/gemma-2-9b-it
model_type:
    value: gemma
quantization:
    value:
        bnb_4bit_compute_dtype: float16
        bnb_4bit_quant_type: nf4
        bnb_4bit_use_double_quant: true
        load_in_4bit: false
training_config:
    value:
        dataloader_num_workers: 4
        dataloader_pin_memory: true
        ddp_find_unused_parameters: false
        eval_steps: 500
        evaluation_strategy: steps
        fp16: true
        gradient_accumulation_steps: 8
        greater_is_better: false
        group_by_length: false
        learning_rate: 0.0002
        load_best_model_at_end: true
        logging_steps: 10
        lr_scheduler_type: cosine
        metric_for_best_model: eval_loss
        num_train_epochs: 3
        per_device_train_batch_size: 1
        remove_unused_columns: false
        report_to:
            - wandb
        save_steps: 250
        save_strategy: steps
        save_total_limit: 3
        warmup_ratio: 0.1
        weight_decay: 0.01
