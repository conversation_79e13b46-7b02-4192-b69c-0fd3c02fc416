{"_comment": "GEMMA T9 - Configurazione CON DeepSpeed ZeRO Stage 2", "_version": "T9.1_DeepSpeed", "_created": "2025-07-09", "_description": "Training Gemma-2-9B-IT con DeepSpeed per ottimizzazione memoria", "model_name_or_path": "google/gemma-2-9b-it", "model_type": "gemma2", "output_dir": "experiments/xml_direct_input/outputs/gemma_t9_deepspeed_1gpu_grad_acc", "data_file": "data/processed/xml_format_optimized/train_set_corrected_90k.json", "val_file": "data/processed/xml_format_optimized/test_set_corrected_10k.json", "_comment_dataset": "Dataset: 90K training + 10K validation examples", "dataset_size_train": 90000, "dataset_size_val": 10000, "task_type": "svg_to_caption", "_comment_batch": "Batch configuration con DeepSpeed", "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 8, "effective_batch_size": 8, "dataloader_num_workers": 4, "dataloader_pin_memory": true, "_comment_training": "Training configuration ottimizzata", "max_steps": 50000, "max_length": 2048, "eval_steps": 250, "save_steps": 250, "logging_steps": 25, "max_eval_samples": 500, "_comment_optimizer": "Optimizer con DeepSpeed", "learning_rate": 5e-05, "lr_scheduler_type": "cosine", "warmup_steps": 1000, "weight_decay": 0.01, "optim": "adamw_torch", "max_grad_norm": 1.0, "_comment_lora": "LoRA configuration", "lora_r": 64, "lora_alpha": 128, "lora_dropout": 0.05, "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_bias": "none", "lora_task_type": "CAUSAL_LM", "_comment_quantization": "4-bit quantization", "load_in_4bit": true, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_use_double_quant": true, "bnb_4bit_quant_type": "nf4", "_comment_precision": "Mixed precision con DeepSpeed", "fp16": true, "bf16": false, "gradient_checkpointing": true, "remove_unused_columns": false, "_comment_deepspeed": "DeepSpeed ZeRO Stage 2 configuration (aggiorna<PERSON> <PERSON>)", "deepspeed": "experiments/xml_direct_input/configs/deepspeed_zero2.json", "_comment_evaluation": "Evaluation strategy", "evaluation_strategy": "steps", "save_strategy": "steps", "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "save_total_limit": 3, "_comment_logging": "Logging configuration", "report_to": "wandb", "run_name": "gemma_t9_deepspeed_zero2", "logging_dir": "experiments/xml_direct_input/logs/gemma_t9_deepspeed", "_comment_distributed": "DeepSpeed gestisce la distribuzione", "ddp_find_unused_parameters": false, "local_rank": -1, "_comment_memory": "Memory optimization con DeepSpeed", "max_memory_MB": 46000, "cpu_offload": true, "pin_memory": true, "_comment_performance": "Performance targets con DeepSpeed", "target_training_time_hours": 24, "estimated_memory_savings_percent": 30}