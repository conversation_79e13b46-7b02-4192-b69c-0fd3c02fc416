{"per_device_train_batch_size": 1, "gradient_accumulation_steps": 8, "per_device_eval_batch_size": 1, "num_train_epochs": 3, "max_steps": 50000, "warmup_steps": 500, "logging_steps": 50, "save_steps": 250, "eval_steps": 500, "evaluation_strategy": "steps", "save_strategy": "steps", "learning_rate": 5e-05, "lr_scheduler_type": "cosine", "weight_decay": 0.01, "fp16": true, "bf16": false, "dataloader_num_workers": 4, "remove_unused_columns": false, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "max_length": 2048, "max_eval_samples": 10000, "lora_r": 64, "lora_alpha": 128, "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_dropout": 0.1, "lora_bias": "none", "lora_task_type": "CAUSAL_LM", "quantization_config": {"load_in_4bit": true, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_use_double_quant": true, "bnb_4bit_quant_type": "nf4", "bnb_4bit_quant_storage": "uint8"}}