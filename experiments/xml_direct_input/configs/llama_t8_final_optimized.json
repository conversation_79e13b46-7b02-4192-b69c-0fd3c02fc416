{"_comment": "LLAMA T8 - Configurazione Completa Finale Ottimizzata DA ZERO", "_version": "T8.0", "_created": "2025-06-18", "_description": "Training finale Llama-3.1-8B-Instruct per SVG captioning con quantizzazione 4-bit, LoRA ottimizzato e dual-GPU DA ZERO", "model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct", "model_type": "llama", "output_dir": "experiments/xml_direct_input/outputs/llama_t8_24h", "data_file": "data/processed/xml_format_optimized/train_set_corrected_90k.json", "val_file": "data/processed/xml_format_optimized/test_set_corrected_10k.json", "_comment_dataset": "Dataset: 90K training + 10K validation examples from 100K SVG-caption pairs", "dataset_size_train": 90000, "dataset_size_val": 10000, "task_type": "svg_to_caption", "_comment_batch": "Batch configuration IDENTICA a Gemma T7 - dual-GPU", "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 8, "effective_batch_size": 16, "dataloader_num_workers": 4, "dataloader_pin_memory": true, "dataloader_drop_last": false, "_comment_training": "Training configuration IDENTICA a Gemma T7 - dual-GPU", "max_steps": 50000, "max_length": 2048, "eval_steps": 250, "save_steps": 250, "logging_steps": 25, "max_eval_samples": 500, "prediction_loss_only": false, "_comment_optimizer": "Optimizer configuration IDENTICA a Gemma T7", "learning_rate": 5e-05, "lr_scheduler_type": "cosine", "warmup_steps": 1000, "warmup_ratio": 0.02, "weight_decay": 0.01, "optim": "adamw_torch", "adam_beta1": 0.9, "adam_beta2": 0.999, "adam_epsilon": 1e-08, "max_grad_norm": 1.0, "_comment_lora": "LoRA configuration IDENTICA a Gemma T7", "lora_r": 64, "lora_alpha": 128, "lora_dropout": 0.05, "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_bias": "none", "lora_task_type": "CAUSAL_LM", "_comment_quantization": "4-bit quantization IDENTICA a Gemma T7", "load_in_4bit": true, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_use_double_quant": true, "bnb_4bit_quant_type": "nf4", "bnb_4bit_quant_storage": "uint8", "_comment_precision": "Mixed precision training IDENTICA a Gemma T7", "fp16": true, "bf16": false, "tf32": true, "gradient_checkpointing": true, "remove_unused_columns": false, "torch_compile": false, "_comment_evaluation": "Evaluation and saving strategy IDENTICA a Gemma T7", "evaluation_strategy": "steps", "save_strategy": "steps", "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "save_total_limit": 5, "save_safetensors": true, "_comment_early_stopping": "Early stopping IDENTICA a Gemma T7", "early_stopping_patience": 15, "early_stopping_threshold": 0.0005, "_comment_logging": "Logging e monitoring configuration", "report_to": "wandb", "run_name": "llama_t8_final_optimized_24h_from_scratch", "logging_dir": "experiments/xml_direct_input/logs/llama_t8", "logging_first_step": true, "logging_nan_inf_filter": true, "_comment_distributed": "Distributed training configuration IDENTICA a Gemma T7 - dual-GPU", "ddp_find_unused_parameters": false, "ddp_backend": "nccl", "ddp_bucket_cap_mb": 25, "ddp_broadcast_buffers": false, "local_rank": -1, "_comment_reproducibility": "Reproducibility settings IDENTICA a Gemma T7", "seed": 42, "data_seed": 42, "transformers_seed": 42, "_comment_memory": "Memory optimization settings IDENTICA a Gemma T7", "max_memory_MB": 46000, "cpu_offload": false, "pin_memory": true, "_comment_generation": "Generation parameters IDENTICA a Gemma T7", "generation_max_length": 150, "generation_num_beams": 1, "generation_do_sample": true, "generation_temperature": 0.7, "generation_top_p": 0.9, "generation_top_k": 50, "generation_repetition_penalty": 1.1, "_comment_hardware": "Hardware specifications IDENTICA a Gemma T7", "target_gpu": "L40S", "gpu_memory_gb": 48, "num_gpus": 2, "cpu_cores": 16, "ram_gb": 64, "_comment_performance": "Performance targets IDENTICA a Gemma T7", "target_training_time_hours": 24, "estimated_steps_per_hour": 2083, "estimated_total_time_hours": 24.0, "checkpoint_frequency_minutes": 60, "_comment_model_specs": "Model architecture specifications - Llama-3.1-8B", "model_size_parameters": "8B", "model_architecture": "Llama-3.1", "context_length": 131072, "vocab_size": 128256, "num_layers": 32, "num_attention_heads": 32, "hidden_size": 4096, "intermediate_size": 14336, "_comment_training_data": "Training data specifications IDENTICA a Gemma T7", "input_format": "SVG XML", "output_format": "Natural language caption", "max_svg_length": 1800, "max_caption_length": 200, "preprocessing": "XML format optimized", "_comment_evaluation_metrics": "Evaluation metrics IDENTICA a Gemma T7", "eval_metrics": ["loss", "perplexity", "bleu4", "cider", "rouge_l", "clip_score"], "_comment_prompt_format": "Llama-3.1 specific prompt format", "prompt_template": "<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescrivi questa immagine SVG:\n{svg_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n", "eos_token": "<|eot_id|>", "pad_token": "<|end_of_text|>", "_comment_final": "Configuration IDENTICA a Gemma T7 per confronto equo tra Llama-3.1-8B e Gemma-2-9B"}