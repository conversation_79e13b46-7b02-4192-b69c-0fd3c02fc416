{"model_name_or_path": "google/gemma-2-9b-it", "data_file": "data/processed/xml_format_optimized/train_set_corrected_90k.json", "validation_file": "data/processed/xml_format_optimized/test_set_corrected_10k.json", "output_dir": "experiments/xml_direct_input/outputs/gemma_t9_gradient_accumulation", "overwrite_output_dir": true, "do_train": true, "do_eval": true, "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 8, "learning_rate": 5e-05, "num_train_epochs": 3, "max_steps": 50000, "warmup_steps": 1000, "logging_steps": 25, "save_steps": 250, "eval_steps": 250, "evaluation_strategy": "steps", "save_strategy": "steps", "lr_scheduler_type": "cosine", "weight_decay": 0.01, "fp16": true, "bf16": false, "dataloader_num_workers": 4, "remove_unused_columns": false, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "save_total_limit": 3, "max_eval_samples": 500, "max_length": 2048, "lora_r": 64, "lora_alpha": 128, "lora_dropout": 0.05, "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_bias": "none", "task_type": "CAUSAL_LM", "load_in_4bit": false, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_quant_type": "nf4", "bnb_4bit_use_double_quant": true}