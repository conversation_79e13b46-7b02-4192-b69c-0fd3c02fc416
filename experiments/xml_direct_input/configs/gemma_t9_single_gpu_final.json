{"model_name": "google/gemma-2-9b-it", "model_type": "gemma", "training_config": {"num_train_epochs": 3, "per_device_train_batch_size": 1, "gradient_accumulation_steps": 16, "learning_rate": 0.0002, "weight_decay": 0.01, "warmup_ratio": 0.1, "lr_scheduler_type": "cosine", "logging_steps": 10, "save_steps": 250, "eval_steps": 500, "save_total_limit": 3, "dataloader_num_workers": 4, "remove_unused_columns": false, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "evaluation_strategy": "steps", "save_strategy": "steps", "fp16": true, "ddp_find_unused_parameters": false, "dataloader_pin_memory": true, "group_by_length": false, "report_to": ["wandb"]}, "lora_config": {"r": 64, "lora_alpha": 128, "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_dropout": 0.05, "bias": "none", "task_type": "CAUSAL_LM"}, "data_config": {"max_length": 2048, "truncation": true, "padding": "max_length"}, "quantization": {"load_in_4bit": false, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_quant_type": "nf4", "bnb_4bit_use_double_quant": true}}